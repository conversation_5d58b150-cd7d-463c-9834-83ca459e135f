from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.core.mail import send_mail
from Authentication.customer_verifier import CustomJWTAuthentication
from helpers.otp import generate_otp
from helpers.str_to_bool import convert_to_bool
from helpers.decode_id import decode_token
from helpers.generate_jwt_token import generate_jwt
from helpers.password_hash import hash_password
from .models import *
from django.conf import settings

class RegisterUserView(APIView):
    def post(self, request):
        email = request.data.get('email')
        profile_picture = request.FILES.get('profile_picture')
        password = hash_password(request.data.get('password'))
        one_signal_id = request.data.get('one_signal_id','')

        required_fields = [email, password]
        if not all(required_fields):
            return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)
        try:

            user = User.objects.get(email=email)
            return Response({'status':False,'message': 'User already exists'}, status=status.HTTP_400_BAD_REQUEST)
        
        except User.DoesNotExist:
            user = User.objects.create(email=email, password=password, one_signal_id=one_signal_id)
            return Response({'status':True,'message': 'User created successfully'}, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
class LoginUserView(APIView):
    def post(self, request):
        email = request.data.get('email')
        password = hash_password(request.data.get('password'))
        one_signal_id = request.data.get('one_signal_id','')
        try:
            user = User.objects.get(email=email)
            if user.password != password:
                return Response({'status':False,'message': 'Invalid password'}, status=status.HTTP_400_BAD_REQUEST)
            if user.one_signal_id != one_signal_id:
                user.one_signal_id = one_signal_id
                user.save()
            token = generate_jwt(user)
            is_profile_created = False
            try:
                user_profile = UserProfile.objects.get(user=user)
                is_profile_created = True
            except UserProfile.DoesNotExist:
                is_profile_created = False
            
            data = {
                'id': user.id,
                'email': user.email,
                'is_profile_created':is_profile_created
            }
            return Response({'status':True,'message': 'User logged in successfully','token':token,'data':data}, status=status.HTTP_200_OK)
        
        except User.DoesNotExist:
            return Response({'status':False,'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    

class ForgotPasswordView(APIView):
    def post(self, request):
        try:
            email = request.data.get('email')

            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                return Response({"detail": "Email not found"}, status=status.HTTP_400_BAD_REQUEST)

            # otp = generate_otp()
            otp = '000000'
            user.otp = otp
            user.save()

            # send_mail(
            #     subject='Password Reset OTP',
            #     message=f'Your OTP is {otp}',
            #     from_email=settings.DEFAULT_FROM_EMAIL,
            #     recipient_list=[email],
            #     fail_silently=False,
            # )
            return Response({'status': True, "message": "OTP has been sent to your email"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"detail": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)


class VerifyOtpView(APIView):
    def post(self, request):
        try:
            email = request.data.get('email')
            otp = request.data.get('otp')

            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                return Response({"detail": "Invalid email"}, status=status.HTTP_400_BAD_REQUEST)
            if user.otp != otp:
                return Response({"detail": "Invalid OTP"}, status=status.HTTP_400_BAD_REQUEST)
            token = generate_jwt(user)
            return Response({'status':True,'message': 'OTP verified successfully','token':token}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"detail": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)
        
    
class SetNewPasswordView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            new_password = request.data.get('new_password')

            try:
                user = User.objects.get(pk=user_id)
            except User.DoesNotExist:
                return Response({"detail": "User not found"}, status=status.HTTP_400_BAD_REQUEST)

            user.password = new_password
            user.save()

            return Response({
                'status': True,
                "message": "Password updated successfully"
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"detail": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

class CreateUserProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            full_name = request.data.get('full_name')
            dob = request.data.get('dob')
            gender = request.data.get('gender')
            prefered_gender = request.data.get('prefered_gender')
            prefered_smoking = request.data.get('prefered_smoking')
            cleaniness = request.data.get('cleaniness')
            is_having_pet = convert_to_bool(request.data.get('is_having_pet'))
            class_standing = request.data.get('class_standing')
            habits_lifestyle = request.data.get('habits_lifestyle', [])
            living_style = request.data.get('living_style', [])
            interests_hobbies = request.data.get('interests_hobbies', [])
            about = request.data.get('about')
            contact_number = request.data.get('contact_number')
            prefered_lease_period = request.data.get('prefered_lease_period', [])
            prefered_locations = request.data.get('prefered_locations', [])
            personality_type_description = request.data.get('personality_type_description', [])
            profile_picture = request.FILES.get('profile_picture')
            profile_pictures = request.FILES.getlist('profile_pictures')

            required_fields = [full_name, dob, gender, prefered_gender, prefered_smoking, cleaniness, is_having_pet, class_standing, habits_lifestyle, living_style, interests_hobbies, about, contact_number, prefered_lease_period, prefered_locations]
            if not all(required_fields):
                return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                user = User.objects.get(id=user_id)
                user_profile = UserProfile.objects.get(user=user)
                return Response({'status':False,'message': 'User profile already exists'}, status=status.HTTP_400_BAD_REQUEST)

            except UserProfile.DoesNotExist:
                user = User.objects.get(id=user_id)
                user_profile = UserProfile.objects.create(user=user, full_name=full_name, dob=dob, gender=gender, prefered_gender=prefered_gender, prefered_smoking=prefered_smoking, cleaniness=cleaniness, is_having_pet=is_having_pet, class_standing=class_standing, habits_lifestyle=habits_lifestyle, living_style=living_style, interests_hobbies=interests_hobbies, about=about, contact_number=contact_number, prefered_lease_period=prefered_lease_period, prefered_locations=prefered_locations, personality_type_description=personality_type_description, profile_picture=profile_picture)
                response_data = {
                    'id': user_profile.id,
                    'full_name': user_profile.full_name,
                    'dob': user_profile.dob,
                    'gender': user_profile.gender,
                    'prefered_gender': user_profile.prefered_gender,
                    'prefered_smoking': user_profile.prefered_smoking,
                    'cleaniness': user_profile.cleaniness,
                    'is_having_pet': user_profile.is_having_pet,
                    'class_standing': user_profile.class_standing,
                    'habits_lifestyle': user_profile.habits_lifestyle,
                    'living_style': user_profile.living_style,
                    'interests_hobbies': user_profile.interests_hobbies,
                    'about': user_profile.about,
                    'contact_number': user_profile.contact_number,
                    'prefered_lease_period': user_profile.prefered_lease_period,
                    'prefered_locations': user_profile.prefered_locations,
                    'personality_type_description': user_profile.personality_type_description,
                    'is_verified': user_profile.is_verified,
                    'is_active': user_profile.is_active,
                    'updated_at': str(user_profile.updated_at),
                    'profile_picture': user_profile.profile_picture.url if user_profile.profile_picture else None,
                    'profile_pictures': []
                }
                if profile_pictures:
                    for picture in profile_pictures:
                        user_profile_picture = UserProfilePictures.objects.create(user_profile=user_profile, picture=picture)
                        if user_profile_picture.picture:
                            response_data['profile_pictures'].append(user_profile_picture.picture.url)
                return Response({'status':True,'message': 'User profile created successfully','data':response_data}, status=status.HTTP_200_OK)
            
            except Exception as e:
                return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
        

class UserProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            user_profile = UserProfile.objects.get(user_id=user_id)
            response_data = {
                'id': user_profile.id,
                'full_name': user_profile.full_name,
                'dob': user_profile.dob,
                'gender': user_profile.gender,
                'prefered_gender': user_profile.prefered_gender,
                'prefered_smoking': user_profile.prefered_smoking,
                'cleaniness': user_profile.cleaniness,
                'is_having_pet': user_profile.is_having_pet,
                'class_standing': user_profile.class_standing,
                'habits_lifestyle': user_profile.habits_lifestyle,
                'living_style': user_profile.living_style,
                'interests_hobbies': user_profile.interests_hobbies,
                'about': user_profile.about,
                'contact_number': user_profile.contact_number,
                'prefered_lease_period': user_profile.prefered_lease_period,
                'prefered_locations': user_profile.prefered_locations,
                'personality_type_description': user_profile.personality_type_description,
                'is_verified': user_profile.is_verified,
                'is_active': user_profile.is_active,
                'updated_at': user_profile.updated_at,
                'profile_picture': user_profile.profile_picture.url if user_profile.profile_picture else '',
                'profile_pictures': []
            }
            profile_pictures = UserProfilePictures.objects.filter(user_profile=user_profile)
            for picture in profile_pictures:
                response_data['profile_pictures'].append(picture.picture.url)

            return Response({'status':True,'message': 'User profile fetched successfully','data':response_data}, status=status.HTTP_200_OK)
        except UserProfile.DoesNotExist:
            return Response({'status':False,'message': 'User profile not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class GetSelectionMenuView(APIView):
    def get(self, request):
        habits_lifestyle = HabitsLifestyle.objects.all()
        living_style = LivingStyle.objects.all()
        interests_hobbies = InterestsHobbies.objects.all()
        response_data = {
            'habits_lifestyle': [],
            'living_style': [],
            'interests_hobbies': []
        }
        for habit in habits_lifestyle:
            response_data['habits_lifestyle'].append({'id':habit.id,'name':habit.name,'icon':habit.icon.url if habit.icon else None})
        for living in living_style:
            response_data['living_style'].append({'id':living.id,'name':living.name,'icon':living.icon.url if living.icon else None})
        for interest in interests_hobbies:
            response_data['interests_hobbies'].append({'id':interest.id,'name':interest.name,'icon':interest.icon.url if interest.icon else None})
        return Response({'status':True,'message': 'Selection menu fetched successfully','data':response_data}, status=status.HTTP_200_OK)
    
#App Data 

class CreateHabitsLifestyleView(APIView):
    def post(self, request):
        name = request.data.get('name')
        icon = request.FILES.get('icon')
        required_fields = [name, icon]
        if not all(required_fields):
            return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            habits_lifestyle = HabitsLifestyle.objects.create(name=name, icon=icon) 
            return Response({'status':True,'message': 'Habits lifestyle created successfully','data':habits_lifestyle.id}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
class CreateLivingStyleView(APIView):
    def post(self, request):
        name = request.data.get('name')
        icon = request.FILES.get('icon')
        required_fields = [name, icon]
        if not all(required_fields):
            return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            living_style = LivingStyle.objects.create(name=name, icon=icon) 
            return Response({'status':True,'message': 'Living style created successfully','data':living_style.id}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
class CreateInterestsHobbiesView(APIView):
    def post(self, request):
        name = request.data.get('name')
        icon = request.FILES.get('icon')
        required_fields = [name, icon]
        if not all(required_fields):
            return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            interests_hobbies = InterestsHobbies.objects.create(name=name, icon=icon) 
            return Response({'status':True,'message': 'Interests hobbies created successfully','data':interests_hobbies.id}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class GetHabitsLifestyleView(APIView):
    def get(self, request):
        habits_lifestyle = HabitsLifestyle.objects.all()
        response_data = []
        for habit in habits_lifestyle:
            response_data.append({'id':habit.id,'name':habit.name,'icon':habit.icon.url if habit.icon else None})
        return Response({'status':True,'message': 'Habits lifestyle fetched successfully','data':response_data}, status=status.HTTP_200_OK)
    
class GetLivingStyleView(APIView):
    def get(self, request):
        living_style = LivingStyle.objects.all()
        response_data = []
        for living in living_style:
            response_data.append({'id':living.id,'name':living.name,'icon':living.icon.url if living.icon else None})
        return Response({'status':True,'message': 'Living style fetched successfully','data':response_data}, status=status.HTTP_200_OK)
    
class GetInterestsHobbiesView(APIView):
    def get(self, request):
        interests_hobbies = InterestsHobbies.objects.all()
        response_data = []
        for interest in interests_hobbies:
            response_data.append({'id':interest.id,'name':interest.name,'icon':interest.icon.url if interest.icon else None})
        return Response({'status':True,'message': 'Interests hobbies fetched successfully','data':response_data}, status=status.HTTP_200_OK)
    

class ReportProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            profile_id = request.data.get('profile_id')
            reason = request.data.get('reason')
            required_fields = [profile_id, reason]
            if not all(required_fields):
                return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)
            user = User.objects.get(pk=user_id)
            profile = UserProfile.objects.get(pk=profile_id)
            try:
                report_profile = ReportProfile.objects.get(user=user, profile=profile)
                return Response({'status':True,'message': 'Profile already reported'}, status=status.HTTP_200_OK)
            except ReportProfile.DoesNotExist:
                report_profile = ReportProfile.objects.create(user=user, profile=profile, reason=reason)
                return Response({'status':True,'message': 'Profile reported successfully'}, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({'status':False,'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserProfile.DoesNotExist:
            return Response({'status':False,'message': 'Profile not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
        
        
class BlockProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            profile_id = request.data.get('profile_id')
            required_fields = [profile_id]
            if not all(required_fields):
                return Response({'status':False,'message': 'All fields are required'}, status=status.HTTP_400_BAD_REQUEST)

            # Get the User and UserProfile instances
            user = User.objects.get(id=user_id)
            blocked_profile = UserProfile.objects.get(id=profile_id)

            try:
                block_profile = BlockProfile.objects.get(user=user, blocked_profile=blocked_profile)
                return Response({'status':True,'message': 'Profile already blocked'}, status=status.HTTP_200_OK)
            except BlockProfile.DoesNotExist:
                block_profile = BlockProfile.objects.create(user=user, blocked_profile=blocked_profile)
                return Response({'status':True,'message': 'Profile blocked successfully'}, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({'status':False,'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserProfile.DoesNotExist:
            return Response({'status':False,'message': 'Profile not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
#Edit Profile

class EditProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        token = request.headers.get('Authorization')
        user_id = decode_token(token)
        try:
            user_profile = UserProfile.objects.get(user_id=user_id)
            if not user_profile:
                return Response({'status':False,'message': 'User profile not found'}, status=status.HTTP_400_BAD_REQUEST)
            full_name = request.data.get('full_name')
            dob = request.data.get('dob')
            gender = request.data.get('gender')
            prefered_gender = request.data.get('prefered_gender')
            prefered_smoking = request.data.get('prefered_smoking')
            cleaniness = request.data.get('cleaniness')
            is_having_pet = convert_to_bool(request.data.get('is_having_pet'))
            class_standing = request.data.get('class_standing')
            habits_lifestyle = request.data.get('habits_lifestyle', [])
            living_style = request.data.get('living_style', [])
            interests_hobbies = request.data.get('interests_hobbies', [])
            about = request.data.get('about')
            contact_number = request.data.get('contact_number')
            prefered_lease_period = request.data.get('prefered_lease_period', [])
            prefered_locations = request.data.get('prefered_locations', [])
            personality_type_description = request.data.get('personality_type_description', [])
            profile_picture = request.FILES.get('profile_picture')
            profile_pictures = request.FILES.getlist('profile_pictures')

            if full_name:
                user_profile.full_name = full_name
            if dob:
                user_profile.dob = dob
            if gender:
                user_profile.gender = gender
            if prefered_gender:
                user_profile.prefered_gender = prefered_gender
            if prefered_smoking:
                user_profile.prefered_smoking = prefered_smoking
            if cleaniness:
                user_profile.cleaniness = cleaniness
            if is_having_pet:
                user_profile.is_having_pet = is_having_pet
            if class_standing:
                user_profile.class_standing = class_standing
            if habits_lifestyle:
                user_profile.habits_lifestyle = habits_lifestyle
            if living_style:
                user_profile.living_style = living_style
            if interests_hobbies:
                user_profile.interests_hobbies = interests_hobbies
            if about:
                user_profile.about = about
            if contact_number:
                user_profile.contact_number = contact_number
            if prefered_lease_period:
                user_profile.prefered_lease_period = prefered_lease_period
            if prefered_locations:
                user_profile.prefered_locations = prefered_locations
            if personality_type_description:
                user_profile.personality_type_description = personality_type_description
            if profile_picture:
                user_profile.profile_picture = profile_picture
            if profile_pictures:
                for picture in profile_pictures:
                    user_profile_picture = UserProfilePictures.objects.create(user_profile=user_profile, picture=picture) 
            user_profile.save()
            return Response({'status':True,'message': 'User profile updated successfully'}, status=status.HTTP_200_OK)
        except UserProfile.DoesNotExist:
            return Response({'status':False,'message': 'User profile not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status':False,'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)